package com.wonderslate_v2

import grails.gorm.transactions.Transactional
import com.wonderslate_v2.usermanagement.User
import grails.plugin.springsecurity.SpringSecurityService

class AuthenticationService {
    SpringSecurityService springSecurityService

    def serviceMethod() {

    }

    @Transactional('wsuser')
    def authenticate(String rawUsername, String rawPassword, def session) {
        String username = "1_" + rawUsername
        println(username)
        def user = User.findByUsername(username)

        if (!user) {
            throw new RuntimeException("User not found")
        }

        // Check if password encoder is available and password matches
        if (springSecurityService?.passwordEncoder) {
            String encodedPassword = user.password

            // Handle legacy passwords without algorithm prefix
            if (encodedPassword && !encodedPassword.startsWith('{')) {
                // Add bcrypt prefix for legacy passwords
                encodedPassword = '{bcrypt}' + encodedPassword
            }

            if (!springSecurityService.passwordEncoder.matches(rawPassword, encodedPassword)) {
                throw new RuntimeException("Invalid password")
            }
            if(springSecurityService.passwordEncoder.matches(rawPassword, encodedPassword)){
                session.user = user
            }
        } else {
            println("Password encoder not available")
            if (user.password != rawPassword) {
                throw new RuntimeException("Invalid password")
            }
        }

        return user
    }
}
